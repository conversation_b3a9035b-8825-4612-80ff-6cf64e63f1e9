﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs 1.3
import FluentUI 1.0
import "qrc:/qml/control/common"
import "qrc:/SimpleUI/qml"
import "qrc:/qml/gv"

Rectangle {
    id: variable_list

    property var deviceName
    property var searchowned
    property var searchType
    property string searchScope: "Global"
    property var selectdData: []
    property var dataTypeList: []
    property var modbusrwList: ["N", "R", "RW"]

    property bool isCheckedAll: false
    property var specialType: ["TIME", "DATE", "TIME_OF_DAY", "DATE_AND_TIME", "STRING"]
    property string filePath: ""

    ColumnLayout {
        id: column_layout
        anchors.fill: parent
        spacing: 0

        RowLayout {
            id: row_layout
            Layout.fillWidth: true
            Layout.maximumHeight: 36
            Layout.leftMargin: 10
            Layout.rightMargin: 10
            spacing: 10

            QkButton {
                id: add_row
                text: "添加"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                visible: variable_list.searchType !== "IO"
                onClicked: {
                    let name_prefix = "NewVar_"
                    let address = ""
                    if (searchType === "IO") {
                        address = "%I" + VariableManage.getNextVariableStartAddress(deviceName, searchowned, searchType)
                        name_prefix = "NewIO_"
                    }
                    if (searchType === "M") {
                        address = "%M" + VariableManage.getNextVariableStartAddress(deviceName, searchowned, searchType)
                        name_prefix = "NewM_"
                    }
                    if (searchType !== "IO") {
                        if (VariableManage.addVariable(
                            deviceName,
                            searchScope,
                            searchowned,
                            searchType,
                            name_prefix + GlobalVariable.getRandomString(5),
                            "BOOL",
                            0,
                            address)
                        ) {
                            getBindData()
                        } else {
                            message_dialog.show("添加变量失败")
                        }
                    }
                }
            }

            QkButton {
                id: delete_row
                text: "删除"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                visible: variable_list.searchType !== "IO"
                onClicked: {
                    if (searchType !== "IO") {
                        let selectList = tree_table.selectionModel()
                        if (selectList.length === 0) {
                            message_dialog.show("请选择需要删除的数据")
                        } else {
                            message_dialog.show("确定删除选中的数据吗？", () => {
                                let flag = true
                                selectList.forEach(item => {
                                    flag = flag && VariableManage.deleteVariable(item.data._key) // 删除变量主体
                                    // flag = flag && VariableManage.deleteVariableByName(deviceName, item.data.name) // 删除变量被引用的化身
                                })
                                getBindData()
                            }, () => {
                            }, "confirm")
                        }
                    }
                }
            }

            QkButton {
                id: import_data
                text: "导入"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                visible: variable_list.searchType !== "IO"
                onClicked: {
                    file_import_dialog.open()
                }
            }

            QkButton {
                id: export_data
                text: "导出"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                onClicked: {
                    file_export_dialog.open()
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
            }

            QkButton {
                id: reload_data
                text: "重新加载"
                Layout.preferredWidth: 80
                Layout.preferredHeight: 25
                onClicked: {
                    getBindData()
                }
            }

            QkButton {
                id: assign_address
                text: "分配ModBus地址"
                Layout.preferredWidth: 140
                Layout.preferredHeight: 25
                visible: variable_list.searchType !== "Global"
                onClicked: {
                    const selectList = tree_table.selectionModel()
                    if (selectList.length === 0) {
                        message_dialog.show("请选择需要分配地址的数据")
                    } else {
                        row_popup.visible = true
                    }
                }
            }
        }

        O_TreeTable {
            id: tree_table
            Layout.fillWidth: true
            Layout.fillHeight: true
            customLineColor: "#fd6b54"
            columnSource: [{
                "title": "ID",
                "dataIndex": "_key",
                "width": 70,
                "minimumWidth": 70,
                "maximumWidth": 70,
                "readOnly": true
            }, {
                "title": tree_table.customItem(column_checkbox),
                "dataIndex": 'isChecked',
                "width": 50,
                "minimumWidth": 50,
                "maximumWidth": 50
            }, {
                "title": '名称',
                "dataIndex": 'name',
                "editDelegate": name_text_field,
                "width": (variable_list.width - 1120) / 3,
                "readOnly": variable_list.searchType === "IO"
            }, {
                "title": '数据类型',
                "dataIndex": 'dataType',
                "editDelegate": data_type_combo,
                "width": 150,
                "readOnly": searchType === "IO"
            }, {
                "title": '地址',
                "dataIndex": 'address',
                "editDelegate": address_text_field,
                "width": 70,
                "readOnly": searchType !== "M"
            }, {
                "title": '长度',
                "dataIndex": 'arrayLength',
                "editDelegate": array_length_spin,
                "width": 80,
                "readOnly": searchType === "IO"
            }, {
                "title": "保持",
                "dataIndex": 'isRetained',
                "width": 60,
                "readOnly": true
            }, {
                "title": '注释',
                "dataIndex": 'description',
                "editDelegate": description_text_field,
                "width": (variable_list.width - 1120) / 3,
                "readOnly": false
            }, {
                "title": '初始值',
                "dataIndex": 'initialValue',
                "editDelegate": initial_value_text_field,
                "width": (variable_list.width - 1120) / 3,
                "readOnly": searchType === "IO"
            }, {
                "title": 'Modbus子站对外地址',
                "dataIndex": 'modbusAddress',
                "editDelegate": modbus_address_spin,
                "width": 160,
                "readOnly": searchType === "Global"
            }, {
                "title": 'Modbus子站对外读写',
                "dataIndex": 'modbusRw',
                "editDelegate": modbus_rw_combo_box,
                "width": 160,
                "readOnly": searchType === "Global"
            }, {
                "title": '创建时间',
                "dataIndex": 'createTime',
                "width": 160,
                "readOnly": true
            }, {
                "title": '上一次修改时间',
                "dataIndex": 'lastModifyTime',
                "width": 160,
                "readOnly": true
            }]
        }
    }

    Rectangle {
        id: row_popup
        anchors.fill: parent
        color: "transparent"
        z: 3
        visible: false
        Rectangle {
            id: row_mask
            anchors.fill: parent
            color: "black"
            opacity: 0.5
            z: 4
        }
        MouseArea {
            anchors.fill: parent
            onWheel: event => {
                event.accepted = true
            }
        }
        Rectangle {
            id: row_popup_content
            width: 500
            height: 200
            z: 5
            radius: 6
            anchors.centerIn: parent
            opacity: 1
            color: "#F7F7F7"
            // 标题
            Text {
                id: row_title
                text: qsTr("分配ModBusAddress")
                anchors {
                    left: parent.left
                    top: parent.top
                    leftMargin: 15
                    topMargin: 10
                }
                font.pixelSize: 16
                color: "#333333"
            }
            // 关闭按钮
            FluIconButton {
                anchors {
                    top: parent.top
                    right: parent.right
                    topMargin: 4
                    rightMargin: 0
                }
                iconSource: FluentIcons.ChromeCloseContrast
                iconSize: 12
                onClicked: {
                    row_popup.visible = false
                }
            }
            Rectangle {
                id: cont_rect
                width: parent.width - 20
                height: 20
                anchors {
                    left: parent.left
                    top: row_title.bottom
                    bottom: parent.bottom
                    leftMargin: 10
                    topMargin: 10
                    bottomMargin: 50
                    rightMargin: 10
                }
                Row {
                    anchors.fill: parent
                    anchors.centerIn: parent
                    spacing: 10
                    FluText {
                        id: modBusAddreess_lable
                        anchors.verticalCenter: parent.verticalCenter
                        text: "ModBusAddreess起始地址:"
                    }
                    SpinBox {
                        id: modBusAddreess_value
                        anchors.verticalCenter: parent.verticalCenter
                        from: 40000
                        // to: 50000
                        to: Math.pow(2, 31) - 1
                        value: 40000
                        editable: true
                        enabled: true
                        font.pixelSize: 14
                    }
                }
            }
            // 底部按钮
            Rectangle {
                width: parent.width - 20
                height: 40
                anchors {
                    top: cont_rect.bottom
                    left: parent.left
                    right: parent.right
                    leftMargin: 10
                    rightMargin: 10
                }
                FluFilledButton {
                    width: parent.width / 2
                    height: 30
                    anchors.centerIn: parent
                    text: qsTr("分配")
                    onClicked: {
                        const value = modBusAddreess_value.value
                        if (value) {
                            const selectList = tree_table.selectionModel()
                            const ids = selectList.filter(item => item.data.modbusRw !== "N").map(item => item.data._key)

                            if (!VariableManage.autoAssignModbusAddresses(ids, value)) {
                                message_dialog.show("变量分配失败")
                            }
                            row_popup.visible = false
                            modBusAddreess_value.value = modBusAddreess_value.from
                            getBindData()
                        }
                    }
                }
            }
        }
    }

    Component {
        id: column_checkbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: variable_list.isCheckedAll
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    if (checked === variable_list.isCheckedAll)
                        return
                    variable_list.isCheckedAll = checked
                    Qt.callLater(() => {
                        tree_table.checkAll(checked)
                    })
                }
            }
        }
    }

    Component {
        id: com_checkbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: rowModel.checked
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    tree_table.checkRow(row, checked)
                    if (checked === variable_list.isCheckedAll)
                        return
                    Qt.callLater(() => {
                        variable_list.isCheckedAll = tree_table.selectionModel().length === tree_table.count()
                    })
                }
            }
        }
    }

    Component {
        id: name_text_field
        TextField {
            property var rowData: tree_table.getRow(row)
            anchors.fill: parent
            text: String(display) || ""
            font.pixelSize: 14
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.name !== text && serviceInterface.checkVariableNameSyntax(deviceName, text)) {
                        const param = {
                            "vid": rowData._key,
                            "scope": rowData.scope,
                            "name": text,
                            "dataType": rowData.dataType,
                            "dataTypeID": rowData.dataTypeID,
                            "arrayLength": rowData.arrayLength,
                            "address": rowData.address,
                            "initialValue": rowData.initialValue,
                            "isRetained": rowData.isRetained.options.isRetained,
                            "description": rowData.description
                        }
                        const res = modifyVariable(param)
                        if (res) {
                            message_dialog.show(res)
                        } else {
                            editTextChaged(text)
                            getBindData()
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: data_type_combo
        ComboBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            textRole: "name"
            valueRole: "vid"
            editText: display
            font.pixelSize: 14
            model: variable_list.dataTypeList
            currentIndex: variable_list.dataTypeList.findIndex(dataType => {
                return dataType.name === display
            })
            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }
            onActivated: {
                let initValue = rowData.initialValue
                let arrLength = rowData.arrayLength
                let varAddr = rowData.address
                switch (editText) {
                    case "TIME":
                        initValue = "T#0ms"
                        arrLength = 1
                        varAddr = ""
                        break
                    case "DATE":
                        initValue = "D#1990-01-01"
                        arrLength = 1
                        varAddr = ""
                        break
                    case "TIME_OF_DAY":
                        initValue = "TOD#00:00:00.000"
                        arrLength = 1
                        varAddr = ""
                        break
                    case "DATE_AND_TIME":
                        initValue = "DT#1990-01-01-00:00:00.000"
                        arrLength = 1
                        varAddr = ""
                        break
                    case "STRING":
                        initValue = ""
                        arrLength = 80
                        varAddr = ""
                        break
                    default:
                        initValue = ""
                        arrLength = 1
                        varAddr = ""
                }
                const param = {
                    "vid": rowData._key,
                    "scope": rowData.scope,
                    "name": rowData.name,
                    "dataType": editText,
                    "dataTypeID": currentValue,
                    "arrayLength": arrLength,
                    "address": varAddr,
                    "initialValue": initValue,
                    "isRetained": rowData.isRetained.options.isRetained,
                    "description": rowData.description
                }
                const res = modifyVariable(param)
                if (res) {
                    message_dialog.show(res)
                } else {
                    editTextChaged(editText)
                    tree_table.setRow(row, {
                        dataTypeID: currentValue,
                        address: varAddr,
                        initialValue: initValue,
                        arrayLength: arrLength
                    })
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: address_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            validator: RegularExpressionValidator {
                regularExpression: {
                    if (variable_list.searchType === "M")
                        return GlobalVariable.addressM_regular
                    return GlobalVariable.address_regular
                }
            }
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.address !== text && text.length > 0) {
                        const param = {
                            "vid": rowData._key,
                            "scope": rowData.scope,
                            "name": rowData.name,
                            "dataType": rowData.dataType,
                            "dataTypeID": rowData.dataTypeID,
                            "arrayLength": rowData.arrayLength,
                            "address": text,
                            "initialValue": rowData.initialValue,
                            "isRetained": rowData.isRetained.options.isRetained,
                            "description": rowData.description
                        }
                        const res = modifyVariable(param)
                        if (res) {
                            message_dialog.show(res)
                        } else {
                            editTextChaged(text)
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: array_length_spin
        SpinBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            from: 1
            to: 30000
            editable: true
            value: Number(display)
            font.pixelSize: 14
            visible: !specialType.includes(rowData.dataType)
            onActiveFocusChanged: {
                if (!activeFocus) {
                    const param = {
                        "vid": rowData._key,
                        "scope": rowData.scope,
                        "name": rowData.name,
                        "dataType": rowData.dataType,
                        "dataTypeID": rowData.dataTypeID,
                        "arrayLength": value,
                        "address": rowData.address,
                        "initialValue": rowData.initialValue,
                        "isRetained": rowData.isRetained.options.isRetained,
                        "description": rowData.description
                    }
                    const res = modifyVariable(param)
                    if (res) {
                        message_dialog.show(res)
                    } else {
                        editTextChaged(value)
                    }

                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: retained_checkbox
        Item {
            property var rowData: tree_table.getRow(row)
            S_CheckBox {
                anchors.centerIn: parent
                Layout.alignment: Qt.AlignVCenter
                checked: options.isRetained
                onCheckedChanged: {
                    if (checked === options.isRetained)
                        return
                    const param = {
                        "vid": rowData._key,
                        "scope": rowData.scope,
                        "name": rowData.name,
                        "dataType": rowData.dataType,
                        "dataTypeID": rowData.dataTypeID,
                        "arrayLength": rowData.arrayLength,
                        "address": rowData.address,
                        "initialValue": rowData.initialValue,
                        "isRetained": checked,
                        "description": rowData.description
                    }
                    const res = modifyVariable(param)
                    if (res) {
                        message_dialog.show(res)
                    } else {
                        const retained = rowData.isRetained
                        retained.options.isRetained = checked
                        tree_table.setRow(row, {"isRetained": retained})
                    }
                }
            }
        }
    }

    Component {
        id: description_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            maximumLength: 20

            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.description !== text) {
                        const param = {
                            "vid": rowData._key,
                            "scope": rowData.scope,
                            "name": rowData.name,
                            "dataType": rowData.dataType,
                            "dataTypeID": rowData.dataTypeID,
                            "arrayLength": rowData.arrayLength,
                            "address": rowData.address,
                            "initialValue": rowData.initialValue,
                            "isRetained": rowData.isRetained.options.isRetained,
                            "description": text
                        }
                        const res = modifyVariable(param)
                        if (res) {
                            message_dialog.show(res)
                        } else {
                            editTextChaged(text)
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: initial_value_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            validator: RegularExpressionValidator {
                regularExpression: {
                    if (rowData.dataType === "TIME") {
                        return GlobalVariable.time_type_regular
                    } else if (rowData.dataType === "DATE") {
                        return GlobalVariable.date_type_regular
                    } else if (rowData.dataType === "TIME_OF_DAY") {
                        return GlobalVariable.time_of_day_type_regular
                    } else if (rowData.dataType === "DATE_AND_TIME") {
                        return GlobalVariable.date_and_time_type_regular
                    } else {
                        return /.*/
                    }
                }
            }
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.initialValue !== text) {
                        const param = {
                            "vid": rowData._key,
                            "scope": rowData.scope,
                            "name": rowData.name,
                            "dataType": rowData.dataType,
                            "dataTypeID": rowData.dataTypeID,
                            "arrayLength": rowData.arrayLength,
                            "address": rowData.address,
                            "initialValue": text,
                            "isRetained": rowData.isRetained.options.isRetained,
                            "description": rowData.description
                        }
                        const msg = GlobalVariable.specialTypeCheck(text, rowData.dataType)
                        if (msg) {
                            message_dialog.show(msg)
                            tableView.closeEditor()
                            return
                        }
                        const res = modifyVariable(param)
                        if (res) {
                            message_dialog.show(res)
                        } else {
                            editTextChaged(text)
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: modbus_address_spin
        SpinBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            from: 40000
            // to: 50000
            to: Math.pow(2, 31) - 1
            editable: true
            enabled: true
            value: Number(display)
            font.pixelSize: 14
            onActiveFocusChanged: {
                if (!activeFocus && rowData.modbusRw !== "N" && value !== rowData.modbusAddress) {
                    // 只有在失去焦点的时候才保存
                    const res = VariableManage.modifyModbusaddressAndRW(rowData._key, value, rowData.modbusRw)
                    if (res) {
                        editTextChaged(value)
                    } else {
                        message_dialog.show("修改变量失败")
                    }

                    tableView.closeEditor()
                } else if (rowData.modbusRw === "N") {
                    // modbusRW为N的情况下不允许修改值
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: modbus_rw_combo_box
        ComboBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            editText: display
            font.pixelSize: 14
            model: variable_list.modbusrwList
            currentIndex: variable_list.modbusrwList.findIndex(item => {
                return item === editText
            })
            onActiveFocusChanged: {
                if (!activeFocus) {
                    let modbusAddress = rowData.modbusAddress
                    if (currentValue === "N") {
                        modbusAddress = ""
                    }
                    const res = VariableManage.modifyModbusaddressAndRW(rowData._key, modbusAddress, currentValue)
                    if (res) {
                        editTextChaged(currentValue)
                        if (currentValue === "N") {
                            getBindData()
                        }
                    } else {
                        message_dialog.show("修改变量失败")
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    FileDialog {
        id: file_import_dialog
        title: "请选择要导入的xlsx文件"
        nameFilters: ["Excel files (*.xlsx)"]
        selectMultiple: false
        onAccepted: {
            filePath = fileUrl.toString().replace("file:///", "")

            if(searchType === "Global")
            {
                message_dialog.show("导入变量会将现有的所有变量删除,以新导入的变量为准,请确认是否需要导入!!!",
                                    importExcel, "", "confirm")
            }
            else
            {
                const res = VariableManage.importVariable(deviceName, filePath)
                if (res.length > 0) {
                    message_dialog.show(res)
                } else {
                    getBindData()
                }
            }
        }
    }

    // 导入excel文件
    function importExcel()
    {
        // 获取导入之前的变量
        const globalVariables = VariableManage.getVariableList(deviceName, searchowned, searchType)
        const result = VariableManage.importGlibalVariable(deviceName, filePath)

        if (result.length <= 0)
        {
            // 循环删除导入之前的变量
            for(let gIndex = 0; gIndex < globalVariables.length; gIndex++)
            {
                VariableManage.deleteVariable(globalVariables[gIndex].vid)
            }
        }
        else
        {
            message_dialog.close()
            message_timer.result = result
            message_timer.stop()
            message_timer.start()
        }

        getBindData()
    }

    Timer {
        id: message_timer
        interval: 500
        repeat: false
        property string result: ""
        onTriggered: {
            message_dialog.show(result)
        }
    }

    FileDialog {
        id: file_export_dialog
        title: "请选择要导出的文件夹"
        selectFolder: true
        onAccepted: {
            const selectList = tree_table.selectionModel()
            const ids = selectList.map(item => item.data._key)
            const res = VariableManage.exportVariableByID(ids, fileUrl.toString().replace("file:///", ""), searchType)

            if (!res) {
                message_dialog.show("导出失败!")
            }
        }
    }

    FluContentDialog {
        id: message_dialog

        property var okfunc
        property var nofunc

        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Input Error") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }

        function show(caption, funcok, funcno, type = "info") {
            message_dialog.okfunc = funcok
            message_dialog.nofunc = funcno
            if (type === "info") {
                message_dialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                message_dialog.buttonFlags = FluContentDialogType.NegativeButton | FluContentDialogType.PositiveButton
            }
            message_dialog.message = caption
            message_dialog.open()
        }
    }

    function modifyVariable(params) {
        if (!params.vid)
            return false

        const result = VariableManage.modifyVariable(
            params.vid,
            params.scope,
            params.name,
            params.dataType,
            params.dataTypeID,
            params.arrayLength,
            params.address,
            params.initialValue,
            params.isRetained,
            params.description
        )
        return result
    }

    function getBindData() {
        const res = VariableManage.getVariableList(deviceName, searchowned, searchType)
        // 检查重复行
        checkDuplicateRows()
        tree_table.dataSource = res.map(item => {
            return {
                "_key": item.vid || "",
                "isChecked": tree_table.customItem(com_checkbox),
                "name": item.name || "",
                "scope": item.scope || "",
                "dataType": item.dataType || "",
                "address": item.address || "",
                "arrayLength": item.arrayLength || "",
                "isRetained": tree_table.customItem(retained_checkbox, {"isRetained": Boolean(item.isRetained)}),
                "description": item.description || "",
                "initialValue": item.initialValue || "",
                "modbusAddress": item.modbusAddress || "",
                "modbusRw": item.ModbusRw || "",
                "createTime": item.createTime || "",
                "lastModifyTime": item.lastModifyTime || "",
                "dataTypeID": item.dataTypeID || ""
            }
        })

        dataTypeList = VariableManage.getAllVariableType(deviceName).filter(dataType => dataType.type === "BASE" || dataType.type === "USER")
        Qt.callLater(() => {
            variable_list.isCheckedAll = false
        })
    }

    // 检查重复行
    function checkDuplicateRows()
    {
        // 获取所有变量
        const variables = VariableManage.getVariableList(deviceName)
        const nameCount = {}
        const duplicateRowIndexs = []

        variables.forEach(item =>{
            if (!nameCount[item.name])
            {
                nameCount[item.name] = []
            }

            nameCount[item.name].push(item.vid)
        })

        for (const name in nameCount)
        {
            if (nameCount[name].length > 1)
            {
                duplicateRowIndexs.push(...nameCount[name])
            }
        }

        tree_table.customLineColorIndexs = duplicateRowIndexs
    }
}
