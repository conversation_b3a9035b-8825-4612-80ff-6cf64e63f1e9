import QtQuick 2.15
import QtQuick.Layouts 1.15
import "qrc:/SimpleUI/qml"
import "qrc:/qml/control/common"


Item {
    id: variable_select
    width: 800
    height: 600

    property string deviceName: ""
    property var global_variable: []
    property var io_variable: []
    property var m_variable: []
    property var local_variable: []

    signal cancel()

    signal confirm(var list)

    QkButtonRow {
        id: title_bar
        width: parent.width
        height: 40

        RowLayout {
            anchors.fill: parent
            spacing: 10

            QkLabel {
                Layout.leftMargin: 10
                text: qsTr("请选择变量类型:")
            }

            QkButton {
                text: "全局变量"
                onClicked: {
                    variable_name.text = ""
                    variable_select.search()
                    setTableData("Global")
                }
            }

            QkButton {
                text: "IO变量"
                onClicked: {
                    variable_name.text = ""
                    variable_select.search()
                    setTableData("IO")
                }
            }

            QkButton {
                text: "M变量"
                onClicked: {
                    variable_name.text = ""
                    variable_select.search()
                    setTableData("M")
                }
            }

            QkButton {
                text: "局部变量"
                onClicked: {
                    variable_name.text = ""
                    variable_select.search()
                    setTableData("Local")
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
            }

            QkTextField {
                id: variable_name
                Layout.preferredWidth: 240
                Layout.alignment: Qt.AlignVCenter
                text: ""
                selectByMouse: true // 启用文本选择
                maximumLength: 30

                Keys.onReturnPressed: {
                    variable_select.search()
                }

                Keys.onEnterPressed: {

                    variable_select.search()
                }
            }

            QkButton {
                Layout.rightMargin: 10
                text: "搜索"
                onClicked: {
                    variable_select.search()
                }
            }
        }
    }

    S_TreeTable {
        id: tree_table
        width: parent.width
        height: parent.height - 100
        anchors.top: title_bar.bottom
        columnSource: [
            {
                title: "变量名称",
                dataIndex: "dataName",
                readOnly: true
            },
            {
                title: "数据类型",
                dataIndex: "dataType",
                readOnly: true
            },
            {
                title: "作用域",
                dataIndex: "scope",
                readOnly: true,
            },
            {
                title: "所属",
                dataIndex: "owned",
                readOnly: true
            },
            {
                title: "数组长度",
                dataIndex: "arrayLength",
                width: 100,
                readOnly: true
            },
            {
                title: "注释",
                dataIndex: "description",
                readOnly: true
            }
        ]
    }

    QkButton {
        isGradientBgColor: true
        text: qsTr("Confirm") + (trans ? trans.transString : "")
        anchors.right: parent.horizontalCenter
        anchors.rightMargin: 50
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 20
        width: 150
        onClicked: {
            confirm(tree_table.getChecked())
        }
    }

    QkButton {
        text: qsTr("Cancel") + (trans ? trans.transString : "")
        anchors.left: parent.horizontalCenter
        anchors.leftMargin: 50
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 15
        width: 150
        onClicked: {
            cancel()
        }
    }

    function search() {
        if (variable_name.text.trim() === "") {
            tree_table.setFilter({})
        } else {
            tree_table.setFilter({
                dataName: variable_name.text,
                dataType: variable_name.text,
                scope: variable_name.text,
                owned: variable_name.text,
                arrayLength: variable_name.text,
                description: variable_name.text
            })
        }
    }

    function setTableData(type) {
        switch (type) {
            case "Global":
                tree_table.dataSource = global_variable
                break
            case "IO":
                tree_table.dataSource = io_variable
                break
            case "M":
                tree_table.dataSource = m_variable
                break
            case "Local":
                tree_table.dataSource = local_variable
                break
        }
    }

    function getBindData() {
        variable_select.global_variable = debugManage.getVariableListFromScopeAndOwned(variable_select.deviceName, ["Global"], "Global.POE")
        variable_select.io_variable = debugManage.getVariableListFromScopeAndOwned(variable_select.deviceName, ["I", "O"], "IOM.POE")
        variable_select.m_variable = debugManage.getVariableListFromScopeAndType(variable_select.deviceName, "Global", "M")
        variable_select.local_variable = debugManage.getVariableListFromScopeAndType(variable_select.deviceName, "Local", "PROGRAM")
        setTableData("Global")
    }
}