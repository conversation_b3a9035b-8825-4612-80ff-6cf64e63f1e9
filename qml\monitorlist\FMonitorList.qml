﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import FluentUI 1.0

import "qrc:/qml"
import "qrc:/qml/control/common"
import "qrc:/qml/control/table"
import "qrc:/qml/gv"

Rectangle {
    id: control
    width: parent ? parent.width : 0
    height: parent ? parent.height : 0
    property var newDataSource
    property var tableModel
    property string deviceName: ""
    property var tableNameList: []
    property var variableDataList: []
    //property string selectedForceText: ""
    //property bool listViewVisible: false
    // 默认下标
    property int selectedIndex: -1
    property string tablename: ""

    property bool isStart: false

    property bool isAddTable: false

    //当前滚动的记录
    property var rowslink: []

    //标题按钮栏
    QkButtonRow {
        width: 240
        height: 40

        QkButton {
            width: 70
            height: 30
            anchors.left: parent.left
            anchors.leftMargin: 30
            anchors.verticalCenter: parent.verticalCenter
            text: "添加表"
            onClicked: {
                addTableName.visible = true
            }
        }

        QkButton {
            id: btn_startMonitor
            width: 70
            height: 30
            enabled: false
            anchors.right: parent.right
            anchors.rightMargin: 30
            anchors.verticalCenter: parent.verticalCenter
            text: control.isStart ? "停止" : "开启"
            onClicked: {
                control.rowslink = table.getCurrentRows()
                console.log("rowslink", rowslink.length)
                if (control.isStart) {
                    control.stopWatch()
                } else {
                    control.addWatch()
                    watchtimer.start()
                    control.isStart = true
                }
            }
        }
    }

    function stopWatch() {
        watchtimer.stop()
        monitor.stopWatch()
        control.isStart = false
    }
    //添加表名
    Rectangle {
        id: addTableName
        width: 300
        height: 130
        color: "#ffffff"
        border.color: "#000000"
        border.width: 1
        radius: 10
        visible: false
        anchors.centerIn: parent
        z: 2

        RowLayout {
            anchors.top: addTableName.top
            anchors.topMargin: 20

            QkLabel {
                text: qsTr("表名称:")
                Layout.topMargin: 5
            }
            QkTextField {
                id: tableName
                selectByMouse: false
                maximumLength: 15
            }
        }

        QkButton {
            isGradientBgColor: true
            text: qsTr("Confirm") + (trans ? trans.transString : "")
            anchors.right: addTableName.right
            anchors.rightMargin: 5
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 30
            width: 100
            onClicked: {
                //检查标名称是否重复
                for (var i = 0; i < tableNameList.length; i++) {
                    var name = tableNameList[i]
                    if (name === tableName.text) {
                        infoBar.showError("表名称重复！！", 5000)
                        return
                    }
                }
                tableNameList.push(tableName.text)
                treeview.model = tableNameList
                tableName.text = ""
                addTableName.visible = false
            }
        }

        QkButton {
            text: qsTr("Cancel") + (trans ? trans.transString : "")
            anchors.left: addTableName.left
            anchors.leftMargin: 5
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 30
            width: 100
            onClicked: {
                tableName.text = ""
                addTableName.visible = false
            }
        }
    }

    //左侧框
    Rectangle {
        width: 240
        height: control.height - 40
        anchors.left: parent.left
        anchors.bottom: parent.bottom
        border.width: 1
        border.color: "gray"

        ListView {
            id: treeview
            anchors.fill: parent
            model: tableNameList
            clip: true
            delegate: Rectangle {
                width: 240
                height: 30
                color: selectedIndex === index ? "#0087DE" : "transparent" // 根据当前项是否被选中设置颜色
                Text {
                    anchors.centerIn: parent
                    text: modelData
                }
                Rectangle {
                    anchors.bottom: parent.bottom
                    width: parent.width
                    height: 1
                    color: "gray"
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        btn_startMonitor.enabled = true
                        treeview.currentIndex = index
                        selectedIndex = index // 设置选中项的索引
                        control.tablename = modelData
                        control.getDataBind()
                    }
                }
            }

            highlightMoveDuration: 200
        }
    }

    //右侧框
    Rectangle {
        id: leftbk
        width: control.width - 240
        height: control.height
        anchors.right: parent.right
        border.color: "black"
        border.width: 1

        JTableView {
            id: table
            property var displayFormat: ["十进制", "十六进制", "二进制"]
            property var isModify: [false, true]
            width: parent.width
            height: parent.height
            checkbox: true
            dataSource: []
            columnIsVisible: false
            funs: [addRow, deleteRow]
            coms: ["添加行", "删除行"]
            columnDataSource: [{
                    "title": '表名称',
                    "dataIndex": 'TableName',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": true,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '编号',
                    "dataIndex": 'ID',
                    "width": 80,
                    "readOnly": true,
                    "hide": false,
                    "minimumWidth": 100
                }, {
                    "title": '变量索引',
                    "dataIndex": 'VarID',
                    "width": 80,
                    "readOnly": true,
                    "hide": false,
                    "minimumWidth": 100
                }, {
                    "title": '变量名称',
                    "dataIndex": 'MonitorName',
                    "width": 200,
                    "hide": true,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '作用域',
                    "dataIndex": 'Scope',
                    "width": 100,
                    "hide": false,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '所属',
                    "dataIndex": 'Owned',
                    "width": 100,
                    "hide": false,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '类型',
                    "dataIndex": 'Type',
                    "width": 100,
                    "readOnly": true,
                    "hide": false,
                    "minimumWidth": 50
                }, {
                    "title": '数据类型',
                    "dataIndex": 'DataType',
                    "width": 100,
                    "hide": true,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '数据类型ID',
                    "dataIndex": 'DataTypeID',
                    "width": 100,
                    "hide": false,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '监视值',
                    "dataIndex": 'MonitoredValue',
                    "width": 100,
                    "hide": true,
                    "readOnly": true,
                    "minimumWidth": 50
                }, {
                    "title": '显示格式',
                    "dataIndex": 'DisplayFormat',
                    "editDelegate": com_DisplayFormat,
                    "width": 120,
                    "hide": true,
                    "readOnly": false,
                    "minimumWidth": 100
                }, {
                    "title": '使用触发器监视',
                    "dataIndex": 'MonitoringWithTrigger',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": false,
                    "readOnly": false,
                    "minimumWidth": 50
                }, {
                    "title": '使用触发器修改',
                    "dataIndex": 'ModifyWithTrigger',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": false,
                    "readOnly": false,
                    "minimumWidth": 50
                }, {
                    "title": '修改值',
                    "dataIndex": 'ModifyValue',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": false,
                    "readOnly": false,
                    "minimumWidth": 50
                }, {
                    "title": '是否修改',
                    "dataIndex": 'IsModify',
                    "editDelegate": com_IsModify,
                    "readOnly": false,
                    "hide": false,
                    "width": 80,
                    "minimumWidth": 50
                }, {
                    "title": '监控注释',
                    "dataIndex": 'Description',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": true,
                    "readOnly": false,
                    "minimumWidth": 50
                }, {
                    "title": '监视状态',
                    "dataIndex": 'State',
                    "editDelegate": com_edit,
                    "width": 80,
                    "hide": false,
                    "readOnly": false,
                    "minimumWidth": 50
                }]

            ListModel {
                id: displayFormatModel
            }

            FluInfoBar {
                id: infoBar
                root: table
            }
            onUpdateAfterDataSourceCallBack: dataSource => {
                                                 newDataSource = dataSource
                                             }

            onCheckBoxChanged: model => {
                                   tableModel = model
                               }
            onRowMovementEnded: {
                control.rowslink = table.getCurrentRows()
                //console.log("onRowMovementEnded rowslink.length",control.rowslink[0].VarID, rowslink.length)
                if (control.isStart && !isAddTable) {
                    control.isAddTable = true
                    //根据当前行进行添加到刷新列表中
                    if (control.rowslink.length > 0) {
                        console.log("rowslink old start len",
                                    control.rowslink[0].VarID,
                                    control.rowslink.length)
                        var starindex = control.rowslink[0].VarID
                        var vlen = control.rowslink.length
                        var vend = starindex + vlen
                        if (starindex < 20) {
                            starindex = 0
                            vlen = vend - starindex
                        }
                        console.log("rowslink new start len", starindex, vlen)
                        monitor.cfgWatchData(starindex, vlen)
                    }
                    control.isAddTable = false
                }
            }
            Component {
                id: com_IsModify
                FluComboBox {
                    anchors.fill: parent
                    focus: true
                    currentIndex: display
                    editable: true
                    model: table.isModify
                    Component.onCompleted: {
                        currentIndex = table.isModify.findIndex(
                                    element => element === Boolean(display))
                        selectAll()
                    }
                    onCommit: {
                        if (editText !== display) {
                            display = editText
                            var currentRow = control.newDataSource[row]
                            VariableManage.modifyMonitorVariable(
                                        currentRow.ID, deviceName,
                                        currentRow.TableName, currentRow.VarID,
                                        currentRow.MonitorName, currentRow.MonitoredValue,
                                        currentRow.DisplayFormat, currentRow.MonitoringWithTrigger,
                                        currentRow.ModifyWithTrigger, currentRow.ModifyValue,
                                        table.isModify[display], currentRow.Description,
                                        currentRow.State)
                        }
                        getDataBind()
                        tableView.closeEditor()
                    }
                }
            }

            Component {
                id: com_DisplayFormat
                FluComboBox {
                    anchors.fill: parent
                    focus: true
                    currentIndex: display
                    editable: true
                    model: table.displayFormat
                    Component.onCompleted: {
                        currentIndex = table.displayFormat.findIndex(
                                    element => element === String(display))
                        selectAll()
                    }
                    onCommit: {
                        if (editText !== display) {
                            display = editText
                            var currentRow = control.newDataSource[row]
                            VariableManage.modifyMonitorVariable(
                                        currentRow.ID, deviceName,
                                        currentRow.TableName, currentRow.VarID,
                                        currentRow.MonitorName, currentRow.MonitoredValue,
                                        editText, currentRow.MonitoringWithTrigger,
                                        currentRow.ModifyWithTrigger, currentRow.ModifyValue,
                                        currentRow.IsModify, currentRow.Description,
                                        currentRow.State)
                        }
                        getDataBind()
                        tableView.closeEditor()
                    }
                }
            }

            Component {
                id: com_edit
                FluTextBox {
                    id: text_box
                    text: display
                    readOnly: false
                    Component.onCompleted: {
                        forceActiveFocus()
                        selectAll()
                    }
                    onCommit: {
                        display = text_box.text
                        var currentRow = control.newDataSource[row]
                        VariableManage.modifyMonitorVariable(
                                    currentRow.ID, deviceName,
                                    currentRow.TableName, currentRow.VarID,
                                    currentRow.MonitorName, currentRow.MonitoredValue,
                                    currentRow.DisplayFormat, currentRow.MonitoringWithTrigger,
                                    currentRow.ModifyWithTrigger, currentRow.ModifyValue,
                                    currentRow.IsModify, currentRow.Description,
                                    currentRow.State)

                        getDataBind()
                        tableView.closeEditor()
                    }
                }
            }
        }
    }

    MainPopup {
        id: add_btn_dialog
        width: parent.width - 100
        height: parent.height - 100
        visible: false
        anchors.centerIn: parent // 在父元素中居中显示

        //按钮
        Rectangle {
            id: borderLeft
            width: add_btn_dialog.width
            height: 30
            anchors.top: add_btn_dialog.top
            anchors.left: parent.left // 左边缘与父项左边缘对齐
            anchors.leftMargin: 1
            border.color: "black"
            border.width: 1
            QkButtonRow {
                QkLabel {
                    id: groupName
                    anchors.left: parent.left
                    anchors.leftMargin: 10
                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("请选择变量类型:")
                }

                RowLayout {
                    anchors.centerIn: parent
                    anchors.left: groupName.left
                    QkButton {
                        text: "全局变量"
                        onClicked: {
                            if (tablename === "") {
                                newInfoBar.showError("请选择或新建表名称!!!", 5000)
                                return
                            }
                            getVariableData("全局变量")
                        }
                    }

                    QkButton {
                        visible: true
                        text: "IO变量"
                        onClicked: {
                            if (tablename === "") {
                                newInfoBar.showError("请选择或新建表名称!!!", 5000)
                                return
                            }
                            getVariableData("IO变量")
                        }
                    }

                    QkButton {
                        text: "M变量"
                        onClicked: {
                            if (tablename === "") {
                                newInfoBar.showError("请选择或新建表名称!!!", 5000)
                                return
                            }
                            getVariableData("M变量")
                        }
                    }

                    QkButton {
                        text: "局部变量"
                        onClicked: {
                            if (tablename === "") {
                                newInfoBar.showError("请选择或新建表名称!!!", 5000)
                                return
                            }
                            getVariableData("局部变量")
                        }
                    }
                }
            }
        }

        //待选择变量
        Rectangle {
            id: borderRight
            width: add_btn_dialog.width
            height: parent.height - 105
            anchors.top: borderLeft.bottom
            anchors.topMargin: 10
            anchors.left: add_btn_dialog.right
            border.color: "black"
            border.width: 1

            JTableView {
                id: variableDataTable
                checkbox: true
                width: parent.width
                height: parent.height
                color: "lightgreen"
                dataSource: []
                columnDataSource: [{
                        "title": '变量名称',
                        "dataIndex": 'dataName',
                        "width": 240,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": 'ID',
                        "dataIndex": 'ID',
                        "readOnly": true,
                        "width": 100,
                        "hide": false,
                        "minimumWidth": 50,
                        "maximumWidth": 150
                    }, {
                        "title": 'VID',
                        "dataIndex": 'VID',
                        "width": 140,
                        "hide": false,
                        "readOnly": true,
                        "minimumWidth": 150
                    }, {
                        "title": '数据类型',
                        "dataIndex": 'dataType',
                        "width": 140,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '数据类型ID',
                        "dataIndex": 'dataTypeID',
                        "width": 140,
                        "hide": false,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '作用域',
                        "dataIndex": 'scope',
                        "width": 140,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '所属',
                        "dataIndex": 'owned',
                        "width": 140,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '数组长度',
                        "dataIndex": 'arrayLength',
                        "width": 140,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '类型',
                        "dataIndex": 'type',
                        "width": 140,
                        "hide": false,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '层级信息',
                        "dataIndex": 'Deep',
                        "readOnly": true,
                        "hide": false,
                        "width": 150,
                        "minimumWidth": 150,
                        "maximumWidth": 500
                    }, {
                        "title": '父节点ID',
                        "dataIndex": 'ParentID',
                        "readOnly": true,
                        "width": 150,
                        "hide": false,
                        "minimumWidth": 150,
                        "maximumWidth": 500
                    }, {
                        "title": '注释',
                        "dataIndex": 'description',
                        "width": 140,
                        "hide": true,
                        "readOnly": true,
                        "minimumWidth": 200
                    }, {
                        "title": '展开',
                        "dataIndex": 'chevron',
                        "width": 140,
                        "hide": false,
                        "readOnly": true,
                        "minimumWidth": 200
                    }]

                FluInfoBar {
                    id: newInfoBar
                    root: variableDataTable
                }

                onCheckBoxChanged: dataSource => {
                                       tableModel = dataSource
                                       //选择数据后进行加入variableDataList
                                       for (var index = 0; index < tableModel.rowCount; index++) {
                                           var row = tableModel.getRow(index)

                                           if (row.checkbox.options.checked
                                               && !row.havechild) {
                                               let newRow = {
                                                   "id": row.ID,
                                                   "name": row.dataName,
                                                   "datatype": row.dataType,
                                                   "datatypeId": row.dataTypeID,
                                                   "scope": row.scope,
                                                   "owned": row.owned,
                                                   "type": row.type,
                                                   "arrayLength": row.arrayLength,
                                                   "desc": row.VID
                                               }
                                               variableDataList.push(newRow)
                                           } else {
                                               if (variableDataList.length > 0) {
                                                   for (var i = 0; i
                                                        < variableDataList.length; i++) {
                                                       if (variableDataList[i].name
                                                           === row.dataName) {
                                                           variableDataList.splice(
                                                               i, 1)
                                                       }
                                                   }
                                               }
                                           }
                                       }
                                   }
            }
        }

        QkButton {
            isGradientBgColor: true
            text: qsTr("Confirm") + (trans ? trans.transString : "")
            anchors.right: parent.horizontalCenter
            anchors.rightMargin: 50
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 30
            width: 150
            onClicked: {

                add_btn_dialog.visible = false // 点击确定按钮后隐藏对话框
                variableDataList = removeDuplicatesByIdAndName(variableDataList)
                if (variableDataList.length > 0) {
                    addRowFunction(control.tablename, variableDataList)
                } else {
                    //根据全选来进行添加
                    addRowBasedFunction(control.tablename)
                }
                variableDataList = []
            }
        }
        QkButton {
            text: qsTr("Cancel") + (trans ? trans.transString : "")
            anchors.left: parent.horizontalCenter
            anchors.leftMargin: 50
            anchors.bottom: parent.bottom
            anchors.bottomMargin: 30
            width: 150
            onClicked: {
                add_btn_dialog.visible = false // 点击确定按钮后隐藏对话框
            }
        }
    }

    Component.onDestruction: {
        watchtimer.stop()
        //control.aryVariable = new Map()
    }

    FluContentDialog {
        id: messageDialog

        property var okfunc
        property var nofunc

        title: qsTr("Notification") + (trans ? trans.transString : "")
        message: qsTr("Notification") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }
        function show(caption, funcok, funcno) {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            messageDialog.message = caption
            messageDialog.open()
        }
    }

    Timer {
        id: watchtimer
        repeat: true
        interval: 2000
        onTriggered: {
            control.updateValue()
        }
    }
    function getChildVariableData(ID, varID, typeName) {
        //数组模式
        let dataSource = []
        let dataSourceList = debugManage.getChildVariableData(ID,
                                                              varID, typeName)
        console.log("debugmanage getChildVariableData",
                    JSON.stringify(dataSourceList))
        //获取数据结构的每层展开
        for (var k = 0; k < dataSourceList.length; k++) {
            getChildType(dataSourceList[k]).forEach(da => dataSource.push(da))
        }
        console.log("debugmanage getChildVariableData dataSource",
                    JSON.stringify(dataSource))
        return dataSource
    }

    function getChildType(data) {
        let ary = []
        //console.log("debugmanage getChildType", JSON.stringify(data))
        let row = {
            "ID": data.ID,
            "VID": data.vid,
            "scope": data.scope,
            "owned": data.owned,
            "type": data.type,
            "dataName": data.name,
            "dataType": data.dataType,
            "dataTypeID": data.dataTypeID,
            "arrayLength": data.arrayLength,
            "description": data.description,
            "Deep": data.Deep,
            "ParentID": data.ParentID,
            "havechild": data.havechild,
            "chevron": !data.havechild,
            "children": []
        }
        if (data.children.length > 0) {
            let childlist = []
            for (var i = 0; i < data.children.length; i++) {
                getChildType(data.children[i]).forEach(da => childlist.push(da))
            }
            row.children = childlist
        }
        ary.push(row)
        return ary
    }

    function getVariableData(variableName) {
        //初始为空
        variableDataTable.dataSource = []
        var dataSourceList = []
        var dataSource = []
        if (variableName === "全局变量") {
            dataSourceList = debugManage.getVariableListFromScopeAndOwned(
                        deviceName, ["Global"], "Global.POE")
            //dataSourceList = monitor.getVarTable()
            //console.log("data :", JSON.stringify(dataSourceList))
            //dataSourceList = VariableManage.getVariableList(deviceName,"Global.POE","Global")
        } else if (variableName === "IO变量") {
            dataSourceList = debugManage.getVariableListFromScopeAndOwned(
                        deviceName, ["I", "O"], "IOM.POE")
            //            dataSourceList = DeviceAndNetworkManage.getAllShowVariablesFromDevice(
            //                        deviceName)
        } else if (variableName === "M变量") {
            dataSourceList = debugManage.getVariableListFromScopeAndType(
                        deviceName, "Global", "M")
            //            dataSourceList = VariableManage.getVariableList(deviceName,
            //                                                            "IOM.POE", "M")
        } else if (variableName === "局部变量") {
            dataSourceList = debugManage.getVariableListFromScopeAndType(
                        deviceName, "Local", "PROGRAM")
            //            dataSourceList = VariableManage.getAllList(deviceName, "Local",
            //                                                       "PROGRAM")
        } else {
            return
        }
        //console.log("debugmanage read source", JSON.stringify(dataSourceList))

        //        for (var i = 0; i < dataSourceList.length; i++) {
        //            let childlist = []
        //            JSON.parse(dataSourceList[i].children).forEach(
        //                        da => childlist.push((da)))
        //            let row = {
        //                "ID": dataSourceList[i].ID,
        //                "VID": dataSourceList[i].vid,
        //                "scope": dataSourceList[i].scope,
        //                "owned": dataSourceList[i].owned,
        //                "type": dataSourceList[i].type,
        //                "dataName": dataSourceList[i].name,
        //                "dataType": dataSourceList[i].dataType,
        //                "dataTypeID": dataSourceList[i].dataTypeID,
        //                "arrayLength": dataSourceList[i].arrayLength,
        //                "description": dataSourceList[i].description,
        //                "Deep": dataSourceList[i].Deep,
        //                "ParentID": dataSourceList[i].ParentID,
        //                "havechild": dataSourceList[i].havechild,
        //                "chevron": !dataSourceList[i].havechild,
        //                "children": childlist
        //                //                "children": dataSourceList[i].havechild ? getChildVariableData(
        //                //                                                              dataSourceList[i].ID,
        //                //                                                              dataSourceList[i].vid,
        //                //                                                              dataSourceList[i].dataType) : []
        //            }
        //            //插入数据源中
        //            dataSource.push(row)
        //        }

        //从源数据中排除已经选中的变量
        //        var oldData = VariableManage.getMonitorList(deviceName,
        //                                                    control.tablename)
        //        console.log("debugmanage oldData", JSON.stringify(oldData))
        //        if (oldData.length > 0) {
        //            for (var a = 0; a < oldData.length; a++) {
        //                var data = oldData[a]
        //                for (var j = 0; j < dataSource.length; j++) {
        //                    if (parseInt(dataSource[j].VID) === parseInt(data.varID)
        //                            && dataSource[j].dataName === data.monitorName) {
        //                        dataSource.splice(j, 1)
        //                        continue
        //                    }
        //                }
        //            }
        //        }
        //        console.log("debugmanage dataSource", JSON.stringify(dataSource))
        variableDataTable.dataSource = dataSourceList
        variableDataTable.updateDataSource(dataSourceList)
    }

    function addRow() {
        variableDataTable.dataSource = []
        variableDataList = []
        add_btn_dialog.visible = true
    }

    // 根据全选来添加行
    function addRowBasedFunction(tablename) {
        for (var index = 0; index < tableModel.rowCount; index++) {
            var row = tableModel.getRow(index)
            if (row.checkbox.options.checked && !row.havechild) {
                let newRow = {
                    "id": row.ID,
                    "name": row.dataName,
                    "datatype": row.dataType,
                    "datatypeId": row.dataTypeID,
                    "scope": row.scope,
                    "owned": row.owned,
                    "type": row.type,
                    "arrayLength": row.arrayLength,
                    "desc": row.VID
                }
                variableDataList.push(newRow)
            }
        }
        if (variableDataList.length > 0) {
            addRowFunction(tablename, variableDataList)
        }
    }

    //添加行
    function addRowFunction(tablename, variableDataList) {
        console.log("addRowFunction", tablename,
                    JSON.stringify(variableDataList))
        if (variableDataList.length > 0 && tablename !== "") {
            let dataTypeList = VariableManage.getVariabTypeJsonArray(deviceName)
            for (var i = 0; i < variableDataList.length; i++) {
                console.log("VariableManage.addMonitorVariable",
                            variableDataList[i].arrayLength)
                VariableManage.addMonitorVariable(
                            deviceName, tablename,
                            variableDataList[i].id, variableDataList[i].name,
                            "", "十进制", "", "", getRandomInt(1),
                            true, variableDataList[i].desc,
                            0, variableDataList[i].scope,
                            variableDataList[i].owned, variableDataList[i].type,
                            variableDataList[i].datatype, variableDataList[i].datatypeId)
            }
            getDataBind()
        }
    }

    //io变量中id计算
    function jisuan(num) {
        var numbers = num.split(',')
        // 将输入字符串以逗号分割成数组
        var result = 0

        // 计算模板结果
        for (var i = 0; i < numbers.length; i++) {
            if (i === 0) {
                result += parseInt(numbers[i]) * 1000000
            } else if (i === 1) {
                result += parseInt(numbers[i]) * 1000
            } else if (i === 2) {
                result += parseInt(numbers[i])
            }
        }

        return result
    }

    function findIndexFromDataTypeList(name) {
        for (var i = 0; i < control.baseTypeList.length; i++) {
            if (control.baseTypeList[i] === name)
                return i
        }
        return 0
    }

    function getRandomInt(max) {
        if (!max)
            max = 255
        return Math.floor(Math.random() * max)
    }

    function getDataBind() {
        tableNameList = VariableManage.getMonitorTableName(deviceName)
        if (control.tablename === "") {
            return
        }
        var dataList = VariableManage.getMonitorList(deviceName,
                                                     control.tablename)
        console.log("FMonitorList dataList", JSON.stringify(dataList))
        var dataSource = []
        for (var i = 0; i < dataList.length; i++) {
            //非数组
            let row = {
                "ID": dataList[i].id,
                "VarID": i,
                "TableName": dataList[i].tableName,
                "MonitorName": dataList[i].monitorName,
                "Scope": dataList[i].scope,
                "Owned": dataList[i].owned,
                "Type": dataList[i].type,
                "DataType": dataList[i].dataType,
                "DataTypeID": dataList[i].dataTypeID,
                "ArrayLength": 1,
                "MonitoredValue": dataList[i].monitoredValue,
                "DisplayFormat": dataList[i].displayFormat,
                "MonitoringWithTrigger": dataList[i].monitoringWithTrigger,
                "ModifyWithTrigger": dataList[i].modifyWithTrigger,
                "ModifyValue": dataList[i].modifyValue,
                "IsModify": dataList[i].isModify,
                "Description": dataList[i].description,
                "State": dataList[i].state,
                "Deep": 0,
                "ParentID": 0,
                "children": []
            }
            dataSource.push(row)
        }
        //let deep = 0
        //var variabTypeDataList = VariableManage.getVariabTypeJsonArray(deviceName)
        //        for (var i = 0; i < dataList.length; i++) {
        //            if (control.tablename === dataList[i].tableName) {
        //                //获取该变量的数组长度
        //                let varInfo = VariableManage.getVariableFromID(
        //                        dataList[i].varID)
        //                //console.log("varInfo.arrayLength", varInfo.arrayLength)
        //                if (varInfo.arrayLength > 1) {
        //                    //数组模式
        //                    let row = {
        //                        "ID": dataList[i].id,
        //                        "VarID": dataList[i].varID,
        //                        "TableName": dataList[i].tableName,
        //                        "MonitorName": dataList[i].monitorName,
        //                        "Scope": dataList[i].scope,
        //                        "Owned": dataList[i].owned,
        //                        "Type": dataList[i].type,
        //                        "DataType": dataList[i].dataType,
        //                        "DataTypeID": dataList[i].dataTypeID,
        //                        "ArrayLength": varInfo.arrayLength,
        //                        "MonitoredValue": dataList[i].monitoredValue,
        //                        "DisplayFormat": dataList[i].displayFormat,
        //                        "MonitoringWithTrigger": dataList[i].monitoringWithTrigger,
        //                        "ModifyWithTrigger": dataList[i].modifyWithTrigger,
        //                        "ModifyValue": dataList[i].modifyValue,
        //                        "IsModify": dataList[i].isModify,
        //                        "Description": dataList[i].description,
        //                        "State": dataList[i].state,
        //                        "Deep": deep,
        //                        "ParentID": 0,
        //                        "children": parseArrayChildrenData(
        //                                        variabTypeDataList,
        //                                        dataList[i].tableName,
        //                                        dataList[i].monitorName,
        //                                        dataList[i].id, dataList[i].varID,
        //                                        dataList[i].dataType,
        //                                        dataList[i].dataTypeID, deep,
        //                                        dataList[i].scope, dataList[i].owned,
        //                                        dataList[i].type, varInfo.arrayLength)
        //                    }
        //                    dataSource.push(row)
        //                } else {
        //                    //非数组
        //                    let row = {
        //                        "ID": dataList[i].id,
        //                        "VarID": dataList[i].varID,
        //                        "TableName": dataList[i].tableName,
        //                        "MonitorName": dataList[i].monitorName,
        //                        "Scope": dataList[i].scope,
        //                        "Owned": dataList[i].owned,
        //                        "Type": dataList[i].type,
        //                        "DataType": dataList[i].dataType,
        //                        "DataTypeID": dataList[i].dataTypeID,
        //                        "ArrayLength": 1,
        //                        "MonitoredValue": dataList[i].monitoredValue,
        //                        "DisplayFormat": dataList[i].displayFormat,
        //                        "MonitoringWithTrigger": dataList[i].monitoringWithTrigger,
        //                        "ModifyWithTrigger": dataList[i].modifyWithTrigger,
        //                        "ModifyValue": dataList[i].modifyValue,
        //                        "IsModify": dataList[i].isModify,
        //                        "Description": dataList[i].description,
        //                        "State": dataList[i].state,
        //                        "Deep": deep,
        //                        "ParentID": 0,
        //                        "children": parseChildrenData(variabTypeDataList,
        //                                                      dataList[i].dataTypeID,
        //                                                      deep, dataList[i].scope,
        //                                                      dataList[i].owned)
        //                    }
        //                    dataSource.push(row)
        //                }
        //            }
        //        }
        parseData(dataSource)
    }
    //添加数组子项
    function parseArrayChildrenData(variabTypeDataList, tableName, monitorName, ID, varID, dataType, dataTypeID, deep, scope, owned, type, arraylength) {
        //数组模式
        let dataSource = []
        deep++
        for (var k = 0; k < arraylength; k++) {
            let row = {
                "ID": ID,
                "VarID": varID,
                "TableName": tableName,
                "MonitorName": monitorName + "[" + k + "]",
                "Scope": scope,
                "Owned": owned,
                "Type": type,
                "DataType": dataType,
                "DataTypeID": dataTypeID,
                "ArrayLength": 1,
                "MonitoredValue": "",
                "DisplayFormat": "十进制",
                "MonitoringWithTrigger": 0,
                "ModifyWithTrigger": 0,
                "ModifyValue": "0",
                "IsModify": true,
                "Description": "",
                "State": 0,
                "ParentID": ID,
                "Deep": deep,
                "children": parseChildrenData(variabTypeDataList, dataTypeID,
                                              deep, scope, owned)
            }
            dataSource.push(row)
        }
        return dataSource
    }
    //添加变量结构体子项
    function parseChildrenData(variabTypeDataList, id, deep, scope, owned) {
        let dataSource = []
        if (id !== 0 && id !== undefined) {
            deep++
            //添加该数据类型的子结构体信息
            for (var i = 0; i < variabTypeDataList.length; i++) {
                let parentId = variabTypeDataList[i].parentID
                if (id === parentId) {
                    let row = {
                        "ID": variabTypeDataList[i].vid,
                        "VarID": i,
                        "TableName": control.tablename,
                        "MonitorName": variabTypeDataList[i].name,
                        "Scope": scope,
                        "Owned": owned,
                        "Type": variabTypeDataList[i].type,
                        "DataType": variabTypeDataList[i].name,
                        "DataTypeID": variabTypeDataList[i].vid,
                        "ArrayLength": 1,
                        "MonitoredValue": "",
                        "DisplayFormat": "十进制",
                        "MonitoringWithTrigger": 0,
                        "ModifyWithTrigger": 0,
                        "ModifyValue": "0",
                        "IsModify": true,
                        "Description": "",
                        "State": 0,
                        "ParentID": id,
                        "Deep": deep,
                        "children": parseChildrenData(variabTypeDataList,
                                                      variabTypeDataList[i].id,
                                                      deep, scope, owned)
                    }
                    dataSource.push(row)
                }
            }
        }
        return dataSource
    }

    function parseData(dataSource) {
        table.dataSource = dataSource
        table.updateDataSource(dataSource)
    }

    //去重
    function removeDuplicatesByIdAndName(removeDuplicatesByIdAndName) {
        var uniqueObjects = []
        var uniqueMap = {}

        for (var i = 0; i < variableDataList.length; ++i) {
            var item = variableDataList[i]
            var key = item.name

            // 如果组合键不存在，则添加到map和新数组中
            if (!uniqueMap[key]) {
                uniqueMap[key] = true
                uniqueObjects.push(item)
            }
        }

        return uniqueObjects
    }

    //获取编号
    function getLastSubstring(str) {
        var lastUnderscoreIndex = str.lastIndexOf("_")
        if (lastUnderscoreIndex !== -1) {
            // 如果找到了 '_'，则返回其后的子字符串
            return str.substring(lastUnderscoreIndex + 1)
        } else {
            return "" // 如果没有找到 '_'，返回空字符串
        }
    }

    //数据转换为10进制
    function conversion10(value, baseName) {
        let newValue
        switch (baseName) {
        case "十六进制":
            newValue = parseInt(value, 16)
            break
        case "十进制":
            newValue = value
            break
        case "二进制":
            newValue = parseInt(value, 2)
            break
        default:
            newValue = value
            break
        }
        return newValue
    }

    //将10进制数据转换为指定进制
    function conversion(value, baseName) {
        let newValue
        switch (baseName) {
        case "十六进制":
            newValue = parseInt(value).toString(16).toUpperCase()
            break
        case "十进制":
            newValue = value
            break
        case "二进制":
            newValue = parseInt(value).toString(2)
            break
        default:
            newValue = value
            break
        }
        return newValue
    }

    //获取选中行数据
    function getCheckedData() {
        var checkedData = []
        var data = JSON.parse(JSON.stringify(tableModel.rows))
        for (var i = 0; i < data.length; i++) {
            let el = data[i]
            if (el.checkbox) {
                let options = el.checkbox.options
                if (options && options.checked) {
                    checkedData.push(el)
                }
            }
        }
        return checkedData
    }

    //删除行
    function deleteRow() {
        let ids = []
        var data = getCheckedData()
        let dle = true
        for (var i = 0; i < data.length; i++) {
            if (data[i].Deep !== 0) {
                dle = false
                break
            }
            ids.push(data[i].ID)
        }
        if (!dle) {
            infoBar.showError("只允许删除父节点!!!", 5000)
            return
        }
        if (ids.length === 0) {
            infoBar.showError("请勾选需要删除的数据!!!", 5000)
        } else {
            dle_message.show(ids)
        }
    }

    function updateValue() {
        var newlist = monitor.parseWatch()
        console.log("rowslink monitor.parseWatch()", JSON.stringify(newlist),
                    control.rowslink.length, newlist.length)
        for (var i = 0; i < control.rowslink.length; i++) {
            var crow = control.rowslink[i]
            for (var l = 0; l < newlist.length; l++) {
                var valuerow = newlist[l]
                if (valuerow.name === crow.Description
                        && valuerow.type === crow.DataType) {
                    control.rowslink[i]["MonitoredValue"] = conversion(
                                valuerow.value, crow.DisplayFormat)
                }
            }
        }
        table.innTable.updateRowValue(control.rowslink)
    }

    function addWatch() {
        monitor.clearWatch()
        for (var l = 0; l < table.dataSource.length; l++) {
            var crow = table.dataSource[l]
            if (crow.Description !== "N/A") {
                let res = monitor.addWatchVar(crow.Description, crow.DataType)
                console.log("crow", crow.MonitorName, crow.Description,
                            crow.DataType, res)
            } else {
                console.log("crow", crow.MonitorName, crow.Description,
                            crow.DataType, "Invalid variable")
            }
        }
        var start = 0
        var v_len = 47
        if (control.rowslink.length > 0) {
            start = control.rowslink[0].VarID
            v_len = control.rowslink.length
            console.log("rowslink link start len", start, v_len)
            if (start < 20) {
                v_len = start + v_len
                start = 0
            }
        }
        console.log("rowslink start start len", start, v_len)
        monitor.cfgWatchData(
                    start,
                    table.dataSource.length > v_len ? v_len : table.dataSource.length)

        monitor.startWatch()

        //monitor.restartWatch()
    }

    FluContentDialog {
        id: dle_message
        property var ids: []
        title: "提示"
        message: "确定要删除选中的数据吗？"
        buttonFlags: FluContentDialogType.NegativeButton | FluContentDialogType.PositiveButton
        negativeText: "取消"
        onNegativeClicked: {
            ids = []
        }
        positiveText: "确定"
        onPositiveClicked: {
            for (var j = 0; j < ids.length; j++) {
                let flag = VariableManage.deleteMonitorVariable(ids[j])
            }
            ids = []
            control.getDataBind()
        }
        function show(ids) {
            dle_message.ids = ids
            dle_message.open()
        }
    }
}
