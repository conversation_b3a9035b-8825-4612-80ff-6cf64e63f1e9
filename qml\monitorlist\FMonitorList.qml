import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import FluentUI 1.0
import "qrc:/qml"
import "qrc:/SimpleUI/qml"
import "qrc:/qml/control/common"

Rectangle {
    id: monitor_table

    property string deviceName: ""
    property var tableNameList: []
    property bool isMonitor: false

    S_Tabs {
        id: tabs
        anchors.fill: parent
        prohibitSwitch: isMonitor

        rightExtra: Row {
            height: parent.height
            spacing: 6

            QkButton {
                text: "添加表"
                width: 80
                height: 25
                anchors.verticalCenter: parent.verticalCenter
                enabled: !monitor_table.isMonitor

                onClicked: {
                    popup.raiseItem = add_table_item
                    popup.open()
                }
            }

            QkButton {
                text: monitor_table.isMonitor ? "关 闭" : "开 启"
                width: 60
                height: 25
                anchors.verticalCenter: parent.verticalCenter
                onClicked: {
                    if (monitor_table.isMonitor) {
                        monitor_table.stopWatch()
                    } else {
                        monitor_table.startWatch()
                        S_Tools.setInterval(updateTable, 1500, "monitor_table")
                    }
                    monitor_table.isMonitor = !monitor_table.isMonitor
                }
            }
        }
    }

    MainPopup {
        id: popup
        anchors.centerIn: parent
    }

    FluContentDialog {
        id: message_dialog

        property var okfunc
        property var nofunc

        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Input Error") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }

        function show(caption, funcok, funcno, type = "info") {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            if (type === "info") {
                messageDialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                messageDialog.buttonFlags = FluContentDialogType.NegativeButton
                    | FluContentDialogType.PositiveButton
            }
            messageDialog.message = caption
            messageDialog.open()
        }
    }

    Component {
        id: add_table_item
        Rectangle {
            width: 400
            height: 170

            QkButtonRow {
                id: title
                z: 10
                QkLabel {
                    anchors.left: parent.left
                    anchors.leftMargin: 10

                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("添加表")
                }
            }

            ColumnLayout {
                anchors.top: title.bottom
                anchors.left: parent.left
                width: parent.width
                height: parent.height - title.height
                spacing: 10

                // 输入区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.margins: 20
                    spacing: 15

                    QkLabel {
                        text: qsTr("表 名:")
                        Layout.alignment: Qt.AlignVCenter
                        Layout.preferredWidth: 60 // 固定标签宽度
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignRight
                    }

                    QkTextField {
                        id: table_name
                        Layout.fillWidth: true
                        Layout.alignment: Qt.AlignVCenter
                        text: ""
                        selectByMouse: true // 启用文本选择
                        maximumLength: 30
                    }
                }

                // 修改后的按钮区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignBottom
                    Layout.bottomMargin: 20
                    spacing: 20

                    // 左侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }

                    // 关闭按钮
                    QkButton {
                        text: qsTr("Close") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.rightMargin: 50
                        onClicked: {
                            popup.close()
                        }
                    }

                    // 确认按钮
                    QkButton {
                        isGradientBgColor: true
                        text: qsTr("Confirm") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.leftMargin: 50
                        onClicked: {
                            if (table_name.text.trim()) {
                                if (tableNameList.includes(table_name.text.trim())) {
                                    return
                                }

                                tabs.addPage(tree_table_component, {
                                    "key": table_name.text.trim(),
                                    "tab": table_name.text.trim(),
                                    "tableData": []
                                })

                                popup.close()
                            } else {
                                message_dialog.show("表名不合法，请重新输入")
                            }
                        }
                    }

                    // 右侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }
                }
            }
        }
    }

    Component {
        id: tree_table_component

        Item {
            id: table_item
            property string key
            property string tab
            property var tableData
            property alias table_view: tree_table

            ColumnLayout {
                id: column_layout
                anchors.fill: parent
                spacing: 0

                RowLayout {
                    id: row_layout
                    Layout.fillWidth: true
                    Layout.maximumHeight: 36
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    spacing: 10

                    QkButton {
                        id: add_var
                        text: "添加变量"
                        Layout.preferredWidth: 80
                        Layout.preferredHeight: 25
                        enabled: !monitor_table.isMonitor

                        onClicked: {
                            const component = Qt.createComponent("qrc:/qml/monitorlist/VariableSelection.qml")
                            if (component.status === Component.Ready) {
                                popup.raiseItem = component
                                popup.open()
                                popup.loadercenter.item.width = monitor_table.width - 100
                                popup.loadercenter.item.height = monitor_table.height - 100
                                popup.loadercenter.item.deviceName = monitor_table.deviceName
                                popup.loadercenter.item.getBindData()
                                popup.loadercenter.item.onCancel.connect(popup.close)
                                popup.loadercenter.item.onConfirm.connect(addVariable)
                            }
                        }
                    }

                    QkButton {
                        id: del_var
                        text: "删除变量"
                        Layout.preferredWidth: 80
                        Layout.preferredHeight: 25
                        enabled: !monitor_table.isMonitor

                        onClicked: {
                            const list = tree_table.getChecked()
                            if (list.length) {
                                message_dialog.show("确定要删除选中的变量吗?", () => {
                                    VariableManage.deleteMonitorVariable(list.map(item => item.id))
                                    let tableData = VariableManage.getMonitorList(deviceName, tabs.activeKey)
                                    tabs.findPage(tabs.activeKey).table_view.dataSource = tableData
                                }, () => {
                                }, "confirm")
                            }
                        }
                    }
                }

                S_TreeTable {
                    id: tree_table
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    dataSource: tableData
                    columnSource: [
                        {
                            title: "表名",
                            dataIndex: "tableName",
                            readOnly: true,
                            width: 120
                        },
                        {
                            title: "变量名称",
                            dataIndex: "monitorName",
                            readOnly: true
                        },
                        {
                            title: "数据类型",
                            dataIndex: "dataType",
                            readOnly: true
                        },
                        {
                            title: "监视值",
                            dataIndex: "monitoredValue",
                            readOnly: true
                        },
                        {
                            title: "显示格式",
                            dataIndex: "displayFormat",
                            editDelegate: com_display_format
                        },
                        {
                            title: "监控注释",
                            dataIndex: "description",
                            readOnly: true
                        }
                    ]
                }
            }
        }
    }

    // 显示格式列代理
    Component {
        id: com_display_format
        ComboBox {
            readonly property var rowData: tableView.getDataByIndex(row)
            model: ["二进制", "十进制", "十六进制"]
            currentIndex: model.findIndex(dataType => dataType === display)

            onActiveFocusChanged: {
                if (!activeFocus) {
                    tableView.closeEditor()
                }
            }
            onActivated: {
                const params = {
                    id: rowData.id,
                    deviceName: deviceName,
                    tableName: rowData.tableName,
                    varID: rowData.varID,
                    monitorName: rowData.monitorName,
                    monitoredValue: rowData.monitoredValue,
                    displayFormat: currentValue,
                    monitoringWithTrigger: rowData.monitoringWithTrigger,
                    modifyWithTrigger: rowData.modifyWithTrigger,
                    modifyValue: rowData.modifyValue,
                    isModify: rowData.isModify,
                    description: rowData.description,
                    state: rowData.state
                }
                if (modifyVariable(params)) {
                    editTextChaged(currentValue)
                    tableView.closeEditor()
                }
            }
        }
    }

    function rangeChanged() {
        const tableView = tabs.findPage(tabs.activeKey).table_view
        const range = tableView.visibleRange()
        const rowData = tableView.getDataByIndex(range.first)
        let start = range.first
        let len = range.last - range.first + 1
        monitor.cfgWatchData(start, len)
    }

    function startWatch() {
        const tableView = tabs.findPage(tabs.activeKey).table_view
        const range = tableView.visibleRange()
        for (let i = 0; i < tableView.view.rows; i++) {
            const rowData = tableView.getDataByIndex(i)
            if (rowData.description !== "N/A") {
                monitor.addWatchVar(rowData.description, rowData.dataType)
            }
        }
        const rowData = tableView.getDataByIndex(range.first)
        let start = range.first
        let len = range.last - range.first + 1
        tableView.visibleRangeChange.connect(rangeChanged)
        monitor.cfgWatchData(start, len)
        monitor.startWatch()
    }

    function updateTable() {
        const tableView = tabs.findPage(tabs.activeKey).table_view
        const range = tableView.visibleRange()
        const res = monitor.parseWatch()
        for (let i = range.first; i <= range.last; i++) {
            const rowData = tableView.getDataByIndex(i)
            for (let j = 0; j < res.length; j++) {
                if (res[j].name === rowData.description && res[j].type === rowData.dataType) {
                    const monitoredValue = parseInt(res[j].value, rowData.displayFormat === "十六进制" ? 16 : rowData.displayFormat === "二进制" ? 2 : 10)
                    tableView.update(rowData.key, {monitoredValue: monitoredValue})
                }
            }
        }
    }

    function stopWatch() {
        const tableView = tabs.findPage(tabs.activeKey).table_view
        tableView.visibleRangeChange.disconnect(rangeChanged)
        monitor.stopWatch()
        S_Tools.clearInterval("monitor_table")
    }

    function addVariable(list) {
        const res = VariableManage.addMonitorVariable(deviceName, tabs.activeKey, list)
        if (res) {
            message_dialog.show(res)
        }
        popup.close()
        let tableData = VariableManage.getMonitorList(deviceName, tabs.activeKey)
        tabs.findPage(tabs.activeKey).table_view.dataSource = tableData
    }

    function modifyVariable(params) {
        const res = VariableManage.modifyMonitorVariable(
            params.id,
            params.deviceName,
            params.tableName,
            params.varID,
            params.monitorName,
            params.monitoredValue,
            params.displayFormat,
            params.monitoringWithTrigger,
            params.modifyWithTrigger,
            params.modifyValue,
            params.isModify,
            params.description,
            params.state
        )
        if (!res) {
            message_dialog.show("修改变量失败")
        }
        return res
    }

    function getDataBind() {
        tabs.model.clear()
        tableNameList = VariableManage.getMonitorTableName(deviceName)
        tableNameList.forEach(item => {
            let tableData = VariableManage.getMonitorList(deviceName, item)
            tabs.addPage(tree_table_component, {
                key: item,
                tab: item,
                tableData: tableData
            })
        })
    }

    Component.onDestruction: {
        stopWatch()
    }
}